use valveprotos::common::CDemoClassInfo;

use crate::fxhash;

#[derive(Clone)]
pub struct ClassInfo {
    pub network_name_hash: u64,
}

pub struct EntityClasses {
    pub classes: usize,
    pub bits: usize,
    pub(crate) class_infos: Vec<ClassInfo>,
}

impl EntityClasses {
    pub fn parse(cmd: CDemoClassInfo) -> Self {
        let class_count = cmd.classes.len();

        // bits is the number of bits to read for entity classes. stolen from
        // butterfly's entity_classes.hpp.
        let bits = (class_count as f32).log2().ceil() as usize;

        let class_infos: Vec<ClassInfo> = cmd
            .classes
            .iter()
            .enumerate()
            .map(|(i, class)| {
                let class_id = class.class_id() as usize;
                assert_eq!(class_id, i, "invliad class id");
                ClassInfo {
                    network_name_hash: fxhash::hash_bytes(class.network_name().as_bytes()),
                }
            })
            .collect();

        Self {
            classes: class_count,
            bits,
            class_infos,
        }
    }

    /// Get class info by ID with bounds checking.
    pub fn by_id(&self, class_id: i32) -> Option<&ClassInfo> {
        self.class_infos.get(class_id as usize)
    }

    /// Get class info by ID without bounds checking.
    ///
    /// # Safety
    ///
    /// The caller must ensure that:
    /// - `class_id` is within the bounds of the class_infos vector
    /// - This is only used in performance-critical paths where the class_id is guaranteed to be valid
    /// - The class_id was previously validated or comes from a trusted source
    pub unsafe fn by_id_unckecked(&self, class_id: i32) -> &ClassInfo {
        // SAFETY: Caller guarantees class_id is within bounds
        self.class_infos.get_unchecked(class_id as usize)
    }
}
